/**
 * 模型配置数组
 *
 * 这个数组包含了所有支持的模型配置信息，包括图标、标签、描述等。
 *
 * 获取方式：

 */
export const modelsAIds = [{
                name: "gpt-5,claude-sonnet-4,gemini-2.5-flash",
                search_model_name: "gpt-5,claude-sonnet-4,gemini-2.5-flash",
                label: "Mixture-of-Agents",
                full_label: "Mixture-of-Agents",

            }, {
                name: "gpt-5",
                label: "GPT-5",
                full_label: "Open AI GPT-5",
            }, {
                name: "gpt-5-high",
                label: "GPT-5 Pro",
                full_label: "Open AI GPT-5 Pro",
            }, {
                name: "o3",
                label: "o3",
                full_label: "Open AI o3",
            }, {
                name: "o3-pro",
                label: "o3-pro",
                full_label: "Open AI o3-pro",
            }, {
                name: "o4-mini-high",
                label: "o4-mini-high",
                full_label: "Open AI o4-mini-high",
            }, {
                name: "claude-3-7-sonnet-thinking",
                label: "<PERSON> Sonnet 3.7 (Thinking)",
                full_label: "Anthropic Claude Sonnet 3.7 (Thinking)",
            }, {
                name: "claude-3-7-sonnet",
                label: "<PERSON> Sonnet 3.7",
                full_label: "Anthropic Claude 3.7 Sonnet",
            }, {
                name: "claude-sonnet-4-thinking",
                label: "<PERSON> Sonnet 4 (Thinking)",
                full_label: "Anthropic Claude Sonnet 4 (Thinking)",
            }, {
                name: "claude-sonnet-4",
                label: "<PERSON> Sonnet 4",
                full_label: "Anthropic <PERSON> Sonnet 4",
            }, {
                name: "claude-opus-4",
                label: "Claude Opus 4",
                full_label: "Anthropic Claude Opus 4",
            }, {
                name: "claude-opus-4-1",
                label: "Claude Opus 4.1",
                full_label: "Anthropic Claude Opus 4.1",
            }, {
                name: "gemini-2.5-flash",
                label: "Gemini 2.5 Flash",
                full_label: "Google Gemini 2.5 Flash",
                support_images: !0,
                support_files: !0
            }, {
                name: "gemini-2.5-pro",
                label: "Gemini 2.5 Pro",
                full_label: "Google Gemini 2.5 Pro",
                support_images: !0,
                support_files: !0
            }, {
                name: "deep-seek-v3",
                label: "DeepSeek V3",
                full_label: "DeepSeek V3",
                support_images: !1,
                support_files: !0,
                hidden: !0
            }, {
                name: "deep-seek-r1",
                label: "DeepSeek R1",
                full_label: "DeepSeek R1",
                support_images: !1,
                support_files: !0
            }, {
                name: "kimi-k2-instruct",
                label: "Kimi K2 Instruct",
                full_label: "Kimi K2 Instruct",
                support_images: !1,
                support_files: !0,
                hidden: !0
            }, {
                name: "groq-kimi-k2-instruct",
                label: "Groq Kimi K2 Instruct",
                full_label: "Groq Kimi K2 Instruct",
            }, {
                name: "grok-4-0709",
                label: "Grok4 0709",
                full_label: "Grok4 0709"
            }];

/**
 * 根据模型名称获取模型配置
 * @param {string} modelName - 模型名称
 * @returns {object|null} 模型配置对象，如果未找到则返回null
 */
export function getModelConfig(modelName) {
    return modelsAIds.find(config => config.name === modelName) || null;
}

/**
 * 获取所有可见的模型配置（排除hidden为true的模型）
 * @returns {Array} 可见的模型配置数组
 */
export function getVisibleModelConfigs() {
    return modelsAIds.filter(config => !config.hidden);
}

/**
 * 创建模型名称到配置的映射对象（用于兼容旧代码）
 * @returns {Object} 模型名称到配置的映射
 */
export function createModelMapping() {
    const mapping = {};
    modelsAIds.forEach(config => {
        mapping[config.name] = config;
    });
    return mapping;
}
