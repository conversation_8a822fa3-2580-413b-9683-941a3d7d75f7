/**
 * 修改请求body中的模型ID
 * @param {string} bodyStr 原始body字符串
 * @param {string} model 目标模型名称
 * @param {Array} modelsAIds 模型配置数组
 * @returns {string} 修改后的body字符串
 */
export function modifyBodyForModel(bodyStr, model, modelsAIds) {
  if (!model) {
    return bodyStr;
  }
  // 从数组中查找对应的模型配置
  try {
    const body = JSON.parse(bodyStr);
    body.extra_data.models[0] = model;
    console.log('RecaptchaTokenScore', printRecaptchaTokenScore(body.g_recaptcha_token));
    return JSON.stringify(body);
  } catch (err) {
    console.error('[modifyBodyForModel] JSON解析失败:', err);
    return bodyStr;
  }
}

/**
 * 提取并打印 g_recaptcha_token 的分数信息
 * @param {string} token 包含 g_recaptcha_token
 * @returns {Object} 包含token信息和分析结果的对象
 */
export function printRecaptchaTokenScore(token) {
  try {

    // 打印token基本信息
    console.log('=== reCAPTCHA Token 分析 ===');
    console.log(`Token长度: ${token.length} 字符`);
    console.log(`Token前50字符: ${token.substring(0, 50)}...`);
    console.log(`Token后50字符: ...${token.substring(token.length - 50)}`);

    // 尝试解析token结构（reCAPTCHA v3 token通常是base64编码的）
    try {
      // 检查是否包含典型的reCAPTCHA v3特征
      const hasTypicalLength = token.length > 1000; // reCAPTCHA v3 tokens通常很长
      const hasValidChars = /^[A-Za-z0-9_-]+$/.test(token);

      console.log(`Token特征分析:`);
      console.log(`- 长度符合reCAPTCHA v3标准: ${hasTypicalLength ? '是' : '否'}`);
      console.log(`- 字符格式有效: ${hasValidChars ? '是' : '否'}`);

      // 计算一个简单的"质量分数"（基于长度和格式）
      let score = 0;
      if (hasTypicalLength) score += 50;
      if (hasValidChars) score += 30;
      if (token.length > 2000) score += 20; // 更长的token通常质量更高

      console.log(`Token质量评分: ${score}/100`);

      return {
        success: true,
        tokenLength: token.length,
        tokenPreview: `${token.substring(0, 50)}...${token.substring(token.length - 50)}`,
        hasTypicalLength,
        hasValidChars,
        qualityScore: score,
        fullToken: token
      };

    } catch (parseError) {
      console.log(`Token解析错误: ${parseError.message}`);
      return {
        success: false,
        error: `Token解析失败: ${parseError.message}`,
        tokenLength: token.length,
        fullToken: token
      };
    }

  } catch (err) {
    console.error('[printRecaptchaTokenScore] JSON解析失败:', err);
    return { error: `JSON解析失败: ${err.message}` };
  }
}
